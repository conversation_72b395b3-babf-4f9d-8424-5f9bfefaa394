/** @odoo-module **/
import { FormController } from "@web/views/form/form_controller";
import { patch } from "@web/core/utils/patch";
import { onMounted, onWillStart, onWillDestroy } from "@odoo/owl";
import { rpc } from "@web/core/network/rpc";

const DEFAULT_COORDS = [36.8, 10.2]; // Tunis (par défaut)

patch(FormController.prototype, {
    setup() {
        super.setup();
        // Initialisation des variables
        this.map = null;
        this.mapContainer = null;
        this.userMarker = null;
        this._routingControl = null;
        this.carriereMarkers = []; // Pour stocker les marqueurs des carrières
        this.routeLine = null; // Pour stocker la ligne d'itinéraire
        this.routeMarkers = []; // Pour stocker les marqueurs de l'itinéraire

        onWillStart(this._loadLeafletAssets);
        onMounted(this._injectButtons.bind(this));
        onWillDestroy(this._cleanup.bind(this));
    },

    async _loadLeafletAssets() {
        if (window.L && L.Routing) {
            return;
        }
        const loadCss = (href) => {
            return new Promise((resolve, reject) => {
                const link = document.createElement("link");
                link.rel = "stylesheet";
                link.href = href;
                link.onload = resolve;
                link.onerror = reject;
                document.head.appendChild(link);
            });
        };
        const loadJs = (src) => {
            return new Promise((resolve, reject) => {
                const script = document.createElement("script");
                script.src = src;
                script.async = true;
                script.defer = true;
                script.onload = resolve;
                script.onerror = reject;
                document.body.appendChild(script);
            });
        };
        try {
            await loadCss("https://unpkg.com/leaflet@1.9.4/dist/leaflet.css");
            await loadCss("https://unpkg.com/leaflet.locatecontrol@0.81.0/dist/L.Control.Locate.min.css");
            await loadCss("https://unpkg.com/leaflet-routing-machine/dist/leaflet-routing-machine.css");
            await loadJs("https://unpkg.com/leaflet@1.9.4/dist/leaflet.js");
            await loadJs("https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js");
            await loadJs("https://unpkg.com/leaflet.locatecontrol@0.81.0/dist/L.Control.Locate.min.js");
        } catch (error) {
            console.error("Erreur lors du chargement des assets Leaflet:", error);
        }
    },

    _injectButtons() {
        // Vérifier si nous sommes dans le modèle 'demande.client'
        if (this.props.resModel !== 'demande.client') {
            return;
        }
        const container = document.querySelector(".o_form_sheet");
        if (!container) return;

        // Supprimer les boutons existants pour éviter les doublons
        const existingMapBtn = document.getElementById("btn-show-map");
        if (existingMapBtn) existingMapBtn.remove();
        const existingFindBtn = document.getElementById("btn-find-carriere");
        if (existingFindBtn) existingFindBtn.remove();

        // Ajouter le bouton de carte
        const mapBtn = document.createElement("button");
        mapBtn.id = "btn-show-map";
        mapBtn.className = "btn btn-primary mr-2";
        mapBtn.innerText = "📍 Afficher carte + position";
        mapBtn.onclick = () => this._showMap();
        container.prepend(mapBtn);

        // Ajouter le bouton de recherche
        const findBtn = document.createElement("button");
        findBtn.id = "btn-find-carriere";
        findBtn.className = "btn btn-success";
        findBtn.innerText = "🚚 Trouver carrière la plus proche";
        findBtn.onclick = () => this._findNearestCarriere();
        container.prepend(findBtn);
    },

    _cleanup() {
        // Nettoyer les ressources lorsque le composant est détruit
        if (this.userMarker && this.map) {
            this.map.removeLayer(this.userMarker);
            this.userMarker = null;
        }

        // Supprimer tous les marqueurs de carrières
        if (this.carriereMarkers && this.map) {
            this.carriereMarkers.forEach(marker => {
                this.map.removeLayer(marker);
            });
            this.carriereMarkers = [];
        }

        // Supprimer les marqueurs de l'itinéraire
        if (this.routeMarkers && this.map) {
            this.routeMarkers.forEach(marker => {
                this.map.removeLayer(marker);
            });
            this.routeMarkers = [];
        }

        // Supprimer la ligne d'itinéraire
        if (this.routeLine && this.map) {
            this.map.removeLayer(this.routeLine);
            this.routeLine = null;
        }

        if (this._routingControl) {
            this._routingControl.remove();
            this._routingControl = null;
        }
        if (this.map) {
            this.map.remove();
            this.map = null;
        }
        if (this.mapContainer) {
            this.mapContainer.remove();
            this.mapContainer = null;
        }
    },

    async _showMap() {
        // Vérifier si nous sommes dans le modèle 'demande.client'
        if (this.props.resModel !== 'demande.client') {
            return;
        }
        if (!window.L) return;

        const container = document.querySelector(".o_form_sheet");
        if (this.mapContainer) {
            this.mapContainer.remove();
        }

        this.mapContainer = document.createElement("div");
        this.mapContainer.id = "map_demande";
        this.mapContainer.style = "height: 450px; margin-top: 10px;";
        container.appendChild(this.mapContainer);

        // Initialiser la carte
        this.map = L.map(this.mapContainer).setView(DEFAULT_COORDS, 12);
        L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
            attribution: '&copy; OpenStreetMap contributors'
        }).addTo(this.map);

        const locateControl = L.control.locate({
            setView: true,
            flyTo: true,
            showCompass: true,
            strings: { title: "📍 Me localiser" },
            locateOptions: {
                enableHighAccuracy: true
            }
        }).addTo(this.map);

        const self = this;
        const resId = this.props.resId;

        this.map.on('locationfound', async function(e) {
            const lat = e.latitude;
            const lng = e.longitude;

            // Vérifier que la carte existe toujours avant de manipuler les marqueurs
            if (!self.map) {
                console.error("La carte n'est plus disponible");
                return;
            }

            // Supprimer le marqueur précédent s'il existe
            if (self.userMarker) {
                self.map.removeLayer(self.userMarker);
                self.userMarker = null;
            }

            // Créer le nouveau marqueur
            self.userMarker = L.marker([lat, lng])
                .addTo(self.map)
                .bindPopup("Votre position")
                .openPopup();

            try {
                // Vérifier si l'enregistrement est nouveau (pas d'ID)
                if (!resId) {
                    // Sauvegarder d'abord l'enregistrement pour obtenir un ID
                    await self.model.root.save();

                    // Récupérer le nouvel ID après sauvegarde
                    const newResId = self.model.root.resId;
                    if (!newResId) {
                        throw new Error("Impossible d'obtenir un ID après sauvegarde");
                    }

                    // Mettre à jour l'ID dans les props
                    self.props.resId = newResId;
                }

                // Vérifier que l'ID est valide
                if (!self.props.resId) {
                    throw new Error("ID d'enregistrement manquant");
                }

                console.log("Envoi de la position au serveur:", {
                    res_id: self.props.resId,
                    latitude: lat,
                    longitude: lng
                });

                const result = await rpc("/demande_client/set_position", {
                    res_id: self.props.resId,
                    latitude: lat,
                    longitude: lng
                });

                console.log("Réponse du serveur:", result);

                if (!result.success) {
                    throw new Error(result.error || "Erreur lors de l'enregistrement");
                }

                // Mettre à jour le modèle local
                await self.model.root.update({
                    latitude: lat,
                    longitude: lng,
                    gps_coordinates: `${lat}, ${lng}`,
                });

                // Recharger les données depuis la base pour s'assurer de la synchronisation
                await self.model.load({ resId: self.props.resId });

                // Forcer la mise à jour de l'interface
                if (self.env && self.env.services && self.env.services.ui) {
                    self.env.services.ui.bus.trigger('update');
                }

                alert("✅ Position enregistrée dans Odoo !");
            } catch (err) {
                console.error("Erreur enregistrement position :", err);
                let errorMessage = "Erreur lors de l'enregistrement de la position";

                if (err.message) {
                    errorMessage += ": " + err.message;
                }

                if (err.data && err.data.message) {
                    errorMessage += ": " + err.data.message;
                }

                // Gestion spécifique des erreurs de validation
                if (err.message.includes("Veuillez d'abord remplir les champs obligatoires") ||
                    (err.data && err.data.message && err.data.message.includes("champs obligatoires"))) {
                    errorMessage = "Veuillez d'abord remplir les champs obligatoires (comme le nom du client) avant de définir votre position.";
                }

                alert(errorMessage);
            }
        });

        const rec = this.model.root;
        if (rec.data.latitude && rec.data.longitude) {
            this.map.setView([rec.data.latitude, rec.data.longitude], 13);
            L.marker([rec.data.latitude, rec.data.longitude])
                .addTo(this.map)
                .bindPopup("Position enregistrée")
                .openPopup();
        }
    },

    async _findNearestCarriere() {
        // Vérifier si nous sommes dans le modèle 'demande.client'
        if (this.props.resModel !== 'demande.client') {
            return;
        }

        const record = this.model.root;

        // Vérifier si les coordonnées sont présentes dans le modèle local
        if (!record.data.latitude || !record.data.longitude) {
            console.log("Coordonnées absentes du modèle local, tentative de récupération depuis la base...");
            try {
                // Récupérer les coordonnées directement depuis la base de données
                const freshData = await rpc("/web/dataset/call_kw/demande.client/read", {
                    model: "demande.client",
                    method: "read",
                    args: [[this.props.resId], ["latitude", "longitude"]],
                    kwargs: {},
                });

                if (freshData && freshData.length > 0) {
                    const { latitude, longitude } = freshData[0];
                    if (latitude && longitude) {
                        console.log("Coordonnées récupérées depuis la base:", { latitude, longitude });
                        // Mettre à jour le modèle local avec les coordonnées fraîches
                        await this.model.root.update({
                            latitude: latitude,
                            longitude: longitude,
                        });
                        // Continuer avec la recherche de carrière
                        return this._findNearestCarriereWithCoords(latitude, longitude);
                    }
                }
                alert("❌ Latitude ou longitude manquante. Cliquez d'abord sur 'Afficher carte + position'.");
                return;
            } catch (err) {
                console.error("Erreur lors de la récupération des coordonnées:", err);
                alert("❌ Impossible de récupérer les coordonnées. Veuillez réessayer.");
                return;
            }
        }

        // Si les coordonnées sont déjà présentes, continuer normalement
        return this._findNearestCarriereWithCoords(record.data.latitude, record.data.longitude);
    },

    async _findNearestCarriereWithCoords(latitude, longitude) {
        try {
            console.log("Recherche de carrières pour les coordonnées:", { latitude, longitude });

            // Récupérer l'ID du matériau sélectionné dans le formulaire
            const record = this.model.root;
            // CORRECTION: S'assurer que materiauId est bien un entier ou null
            let carriereMateriauId = null;
            if (record.data.carriere_materiau_id) {
                // Si c'est un tableau, prendre le premier élément
                if (Array.isArray(record.data.carriere_materiau_id)) {
                    carriereMateriauId = record.data.carriere_materiau_id[0];
                } else {
                    carriereMateriauId = record.data.carriere_materiau_id;
                }
            }

            console.log("Matériau sélectionné:", carriereMateriauId);

            const carrieres = await rpc("/carrieres/geo", {
                client_lat: latitude,
                client_lng: longitude,
                carriere_materiau_id: carriereMateriauId
            });

            console.log("Résultats de recherche:", carrieres);

            // Vérifier si carrieres est un tableau avant de le trier
            if (!Array.isArray(carrieres)) {
                // Si ce n'est pas un tableau, vérifier s'il y a une erreur
                if (carrieres && carrieres.error) {
                    throw new Error(carrieres.error);
                } else {
                    throw new Error("Réponse invalide du serveur");
                }
            }

            if (!carrieres || carrieres.length === 0) {
                alert("Aucune carrière disponible avec le matériau sélectionné.");
                return;
            }

            // Trier les carrières par distance croissante pour s'assurer que la première est la plus proche
            carrieres.sort((a, b) => a.distance_km - b.distance_km);
            const closest = carrieres[0];
            console.log("Carrière la plus proche:", closest);

            // Mettre à jour directement via RPC sans déclencher les validations
            await rpc("/web/dataset/call_kw/demande.client/write", {
                model: "demande.client",
                method: "write",
                args: [[this.props.resId], {
                    carriere_id: closest.id,
                    distance_km: closest.distance_km
                }],
                kwargs: {},
            });

            // Recharger les données pour mettre à jour l'interface
            await this.model.load({ resId: this.props.resId });

            // Afficher toutes les carrières sur la carte
            if (this.map) {
                // Supprimer les anciens marqueurs de carrières
                this.carriereMarkers.forEach(marker => {
                    this.map.removeLayer(marker);
                });
                this.carriereMarkers = [];

                // Ajouter un marqueur pour chaque carrière
                carrieres.forEach(carriere => {
                    const marker = L.marker([carriere.latitude, carriere.longitude])
                        .addTo(this.map)
                        .bindPopup(`
                            <b>${carriere.name}</b><br>
                            Distance: ${carriere.distance_km} km
                        `);

                    // Si c'est la carrière la plus proche, utiliser un icône différent
                    if (carriere.id === closest.id) {
                        marker.setIcon(L.icon({
                            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
                            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
                            iconSize: [25, 41],
                            iconAnchor: [12, 41],
                            popupAnchor: [1, -34],
                            shadowSize: [41, 41]
                        }));
                    }

                    this.carriereMarkers.push(marker);
                });

                // Créer un groupe de tous les marqueurs pour ajuster la vue
                const group = new L.featureGroup(this.carriereMarkers);

                // Ajouter le marqueur de l'utilisateur au groupe s'il existe
                if (this.userMarker) {
                    group.addLayer(this.userMarker);
                }

                // Ajuster la vue pour inclure tous les marqueurs
                this.map.fitBounds(group.getBounds().pad(0.1));

                // Afficher l'itinéraire avec OpenRouteService
                this._showRouteWithORS(latitude, longitude, closest.latitude, closest.longitude);
            }

            alert(`Carrière la plus proche trouvée: ${closest.name} (${closest.distance_km} km)`);

        } catch (err) {
            console.error("Erreur findNearestCarriere :", err);
            let errorMessage = "Erreur lors de la recherche de carrière";

            if (err.message) {
                errorMessage += ": " + err.message;
            }

            if (err.data && err.data.message) {
                errorMessage += ": " + err.data.message;
            }

            alert(errorMessage);
        }
    },

    async _showRouteWithORS(startLat, startLng, endLat, endLng) {
        try {
            // Clé API OpenRouteService
            const orsApiKey = 'eyJvcmciOiI1YjNjZTM1OTc4NTExMTAwMDFjZjYyNDgiLCJpZCI6Ijc0YzAwNjNmZDA3YzRiNTBhNWQzM2I3NDBlY2FmOGYwIiwiaCI6Im11cm11cjY0In0';

            // URL de l'API OpenRouteService
            const orsUrl = 'https://api.openrouteservice.org/v2/directions/driving-car/geojson';

            // Corps de la requête
            const body = {
                coordinates: [
                    [startLng, startLat],
                    [endLng, endLat]
                ]
            };

            // En-têtes de la requête
            const headers = {
                'Authorization': orsApiKey,
                'Content-Type': 'application/json'
            };

            // Effectuer la requête
            const response = await fetch(orsUrl, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(body)
            });

            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }

            const data = await response.json();

            // Extraire les coordonnées de l'itinéraire
            const coordinates = data.features[0].geometry.coordinates;

            // Convertir les coordonnées au format [lat, lng] pour Leaflet
            const latLngs = coordinates.map(coord => [coord[1], coord[0]]);

            // Supprimer l'ancien itinéraire s'il existe
            if (this.routeLine && this.map) {
                this.map.removeLayer(this.routeLine);
            }

            // Supprimer les anciens marqueurs d'itinéraire
            if (this.routeMarkers && this.map) {
                this.routeMarkers.forEach(marker => {
                    this.map.removeLayer(marker);
                });
                this.routeMarkers = [];
            }

            // Créer la nouvelle ligne d'itinéraire
            this.routeLine = L.polyline(latLngs, {
                color: '#FF0000',
                weight: 4,
                opacity: 0.7
            }).addTo(this.map);

            // Ajouter des marqueurs personnalisés pour le départ et l'arrivée
            const startIcon = L.divIcon({
                html: '<i class="fa fa-user" style="color: green; font-size: 24px;"></i>',
                iconSize: [24, 24],
                className: 'custom-div-icon'
            });

            const endIcon = L.divIcon({
                html: '<i class="fa fa-industry" style="color: red; font-size: 24px;"></i>',
                iconSize: [24, 24],
                className: 'custom-div-icon'
            });

            // Ajouter le marqueur de départ
            const startMarker = L.marker([startLat, startLng], { icon: startIcon })
                .addTo(this.map)
                .bindPopup("Départ: Votre position");
            this.routeMarkers.push(startMarker);

            // Ajouter le marqueur d'arrivée
            const endMarker = L.marker([endLat, endLng], { icon: endIcon })
                .addTo(this.map)
                .bindPopup("Arrivée: Carrière");
            this.routeMarkers.push(endMarker);

            // Ajouter un marqueur de voiture au milieu de l'itinéraire
            const midIndex = Math.floor(latLngs.length / 2);
            const carIcon = L.divIcon({
                html: '<i class="fa fa-car" style="color: blue; font-size: 24px;"></i>',
                iconSize: [24, 24],
                className: 'custom-div-icon'
            });

            const carMarker = L.marker(latLngs[midIndex], { icon: carIcon })
                .addTo(this.map)
                .bindPopup("Itinéraire");
            this.routeMarkers.push(carMarker);

            // Ajuster la vue pour inclure l'itinéraire
            this.map.fitBounds(this.routeLine.getBounds());

            // Ajouter le CSS pour les icônes personnalisées si nécessaire
            if (!document.getElementById('custom-leaflet-icons')) {
                const style = document.createElement('style');
                style.id = 'custom-leaflet-icons';
                style.innerHTML = `
                    .custom-div-icon {
                        background: white;
                        border: 2px solid #333;
                        border-radius: 50%;
                        width: 30px !important;
                        height: 30px !important;
                        text-align: center;
                        line-height: 26px !important;
                    }
                `;
                document.head.appendChild(style);
            }

            // Essayer d'obtenir les informations sur les routes
            try {
                const summary = data.features[0].properties.summary;
                const distance = (summary.distance / 1000).toFixed(2) + ' km';
                const duration = Math.round(summary.duration / 60) + ' min';

                // Créer un popup avec les informations de l'itinéraire
                const routeInfo = `
                    <div>
                        <b>Itinéraire</b><br>
                        Distance: ${distance}<br>
                        Durée: ${duration}
                    </div>
                `;

                // Ajouter le popup au marqueur de voiture
                carMarker.setPopupContent(routeInfo);
            } catch (e) {
                console.error("Erreur lors de l'obtention des informations de l'itinéraire:", e);
            }

        } catch (error) {
            console.error("Erreur lors de la récupération de l'itinéraire:", error);

            // En cas d'erreur avec OpenRouteService, utiliser OSRM comme solution de repli
            this._showRouteWithOSRM(startLat, startLng, endLat, endLng);
        }
    },

    _showRouteWithOSRM(startLat, startLng, endLat, endLng) {
        try {
            // URL de l'API OSRM
            const osrmUrl = `https://router.project-osrm.org/route/v1/driving/${startLng},${startLat};${endLng},${endLat}?overview=full&geometries=geojson`;

            // Effectuer la requête
            fetch(osrmUrl)
                .then(response => response.json())
                .then(data => {
                    if (data.routes && data.routes.length > 0) {
                        // Extraire les coordonnées de l'itinéraire
                        const coordinates = data.routes[0].geometry.coordinates;

                        // Convertir les coordonnées au format [lat, lng] pour Leaflet
                        const latLngs = coordinates.map(coord => [coord[1], coord[0]]);

                        // Supprimer l'ancien itinéraire s'il existe
                        if (this.routeLine && this.map) {
                            this.map.removeLayer(this.routeLine);
                        }

                        // Supprimer les anciens marqueurs d'itinéraire
                        if (this.routeMarkers && this.map) {
                            this.routeMarkers.forEach(marker => {
                                this.map.removeLayer(marker);
                            });
                            this.routeMarkers = [];
                        }

                        // Créer la nouvelle ligne d'itinéraire
                        this.routeLine = L.polyline(latLngs, {
                            color: '#0000FF',
                            weight: 4,
                            opacity: 0.7
                        }).addTo(this.map);

                        // Ajouter des marqueurs personnalisés pour le départ et l'arrivée
                        const startIcon = L.divIcon({
                            html: '<i class="fa fa-user" style="color: green; font-size: 24px;"></i>',
                            iconSize: [24, 24],
                            className: 'custom-div-icon'
                        });

                        const endIcon = L.divIcon({
                            html: '<i class="fa fa-industry" style="color: red; font-size: 24px;"></i>',
                            iconSize: [24, 24],
                            className: 'custom-div-icon'
                        });

                        // Ajouter le marqueur de départ
                        const startMarker = L.marker([startLat, startLng], { icon: startIcon })
                            .addTo(this.map)
                            .bindPopup("Départ: Votre position");
                        this.routeMarkers.push(startMarker);

                        // Ajouter le marqueur d'arrivée
                        const endMarker = L.marker([endLat, endLng], { icon: endIcon })
                            .addTo(this.map)
                            .bindPopup("Arrivée: Carrière");
                        this.routeMarkers.push(endMarker);

                        // Ajouter un marqueur de voiture au milieu de l'itinéraire
                        const midIndex = Math.floor(latLngs.length / 2);
                        const carIcon = L.divIcon({
                            html: '<i class="fa fa-car" style="color: blue; font-size: 24px;"></i>',
                            iconSize: [24, 24],
                            className: 'custom-div-icon'
                        });

                        const carMarker = L.marker(latLngs[midIndex], { icon: carIcon })
                            .addTo(this.map)
                            .bindPopup("Itinéraire");
                        this.routeMarkers.push(carMarker);

                        // Ajuster la vue pour inclure l'itinéraire
                        this.map.fitBounds(this.routeLine.getBounds());

                        // Ajouter le CSS pour les icônes personnalisées si nécessaire
                        if (!document.getElementById('custom-leaflet-icons')) {
                            const style = document.createElement('style');
                            style.id = 'custom-leaflet-icons';
                            style.innerHTML = `
                                .custom-div-icon {
                                    background: white;
                                    border: 2px solid #333;
                                    border-radius: 50%;
                                    width: 30px !important;
                                    height: 30px !important;
                                    text-align: center;
                                    line-height: 26px !important;
                                }
                            `;
                            document.head.appendChild(style);
                        }

                        // Essayer d'obtenir les informations sur les routes
                        try {
                            const route = data.routes[0];
                            const distance = (route.distance / 1000).toFixed(2) + ' km';
                            const duration = Math.round(route.duration / 60) + ' min';

                            // Créer un popup avec les informations de l'itinéraire
                            const routeInfo = `
                                <div>
                                    <b>Itinéraire</b><br>
                                    Distance: ${distance}<br>
                                    Durée: ${duration}
                                </div>
                            `;

                            // Ajouter le popup au marqueur de voiture
                            carMarker.setPopupContent(routeInfo);
                        } catch (e) {
                            console.error("Erreur lors de l'obtention des informations de l'itinéraire OSRM:", e);
                        }
                    }
                })
                .catch(error => {
                    console.error("Erreur lors de la récupération de l'itinéraire OSRM:", error);
                });
        } catch (error) {
            console.error("Erreur lors de l'affichage de l'itinéraire OSRM:", error);
        }
    }
}); 