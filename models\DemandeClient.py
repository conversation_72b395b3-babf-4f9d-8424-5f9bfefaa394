# -*- coding: utf-8 -*-
from odoo import models, fields, api
from odoo.exceptions import UserError
import requests
import logging
from math import ceil

_logger = logging.getLogger(__name__)

class DemandeClient(models.Model):
    _name = 'demande.client'
    _description = 'Demande de matériaux client'

    name_client = fields.Char(string="Référence", required=True)
    carriere_id = fields.Many2one('carriere', string='Carrière la plus proche')  # Champ non obligatoire
    gps_coordinates = fields.Char(string="Coordonnées Client")
    latitude = fields.Float(string="Latitude client")
    longitude = fields.Float(string="Longitude client")
    carriere_materiau_id = fields.Many2one(
        'carriere.materiau',
        string='Matériau extrait',
        domain="[('carriere_id', '=', carriere_id)]"
    )
    quantite = fields.Float(string='Quantité demandée')
    distance_km = fields.Float(string='Distance (km)')
    camion_id = fields.Many2one('camion', string='Camion')
    nombre_camions = fields.Integer(string='Nombre de camions', compute='_compute_nombre_camions', store=True)
    cout_total = fields.Float(string='Coût total', compute='_compute_cout_total', store=True)

    @api.model_create_multi
    def create(self, vals_list):
        _logger.info("Création de nouvelles demandes client: %s", vals_list)
        for vals in vals_list:
            _logger.info("Traitement de la demande: %s", vals)
            updated_vals = self._update_lat_lon_from_gps(vals.get('gps_coordinates'))
            vals.update(updated_vals)
            _logger.info("Valeurs après mise à jour GPS: %s", vals)
        result = super().create(vals_list)
        _logger.info("Demandes créées avec succès: %s", result.ids)
        return result

    def write(self, vals):
        _logger.info("Mise à jour de la demande client ID %s: %s", self.id, vals)
        original_values = {
            'latitude': self.latitude,
            'longitude': self.longitude,
            'gps_coordinates': self.gps_coordinates,
            'carriere_materiau_id': self.carriere_materiau_id.id if self.carriere_materiau_id else False
        }

        if 'gps_coordinates' in vals and vals.get('gps_coordinates'):
            _logger.info("Mise à jour des coordonnées GPS: %s", vals.get('gps_coordinates'))
            updated_vals = self._update_lat_lon_from_gps(vals.get('gps_coordinates'))
            vals.update(updated_vals)
            _logger.info("Valeurs après mise à jour GPS: %s", vals)

        # Si on met à jour la carrière, vérifier si le matériau actuel est disponible dans cette carrière
        if 'carriere_id' in vals and vals.get('carriere_id') and self.carriere_materiau_id:
            nouvelle_carriere = self.env['carriere'].browse(vals['carriere_id'])
            if nouvelle_carriere.exists():
                # Vérifier si le matériau actuel est disponible dans la nouvelle carrière
                materiau_disponible = self.env['carriere.materiau'].search([
                    ('carriere_id', '=', nouvelle_carriere.id),
                    ('id', '=', self.carriere_materiau_id.id)
                ])
                if not materiau_disponible:
                    # Si le matériau n'est pas disponible, on ne le change pas
                    _logger.info(
                        "Le matériau ID %s n'est pas disponible dans la carrière %s, conservation du matériau actuel",
                        self.carriere_materiau_id.id, nouvelle_carriere.name)
                    # On empêche la mise à jour du matériau
                    vals.pop('carriere_materiau_id', None)

        result = super().write(vals)

        # Log des changements de coordonnées
        if 'latitude' in vals or 'longitude' in vals or 'gps_coordinates' in vals:
            _logger.info(
                "Coordonnées mises à jour pour la demande %s - Avant: %s, Après: %s",
                self.id,
                original_values,
                {
                    'latitude': self.latitude,
                    'longitude': self.longitude,
                    'gps_coordinates': self.gps_coordinates
                }
            )

        _logger.info("Mise à jour terminée pour la demande %s", self.id)
        return result

    def _update_lat_lon_from_gps(self, gps):
        _logger.info("Mise à jour des coordonnées à partir de GPS: %s", gps)
        if not gps:
            _logger.warning("Aucune coordonnée GPS fournie")
            return {'latitude': False, 'longitude': False}

        try:
            # Vérifier si les coordonnées sont déjà au format "lat,lon"
            if ',' in gps:
                lat, lon = gps.split(',')
                lat = float(lat.strip())
                lon = float(lon.strip())
                _logger.info("Coordonnées extraites du format lat,lon: lat=%s, lon=%s", lat, lon)

                # Validation des coordonnées
                if not (-90 <= lat <= 90):
                    _logger.error("Latitude invalide: %s (doit être entre -90 et 90)", lat)
                    return {}
                if not (-180 <= lon <= 180):
                    _logger.error("Longitude invalide: %s (doit être entre -180 et 180)", lon)
                    return {}

                return {
                    'latitude': lat,
                    'longitude': lon,
                    'gps_coordinates': gps.strip()
                }

            # Sinon, utiliser le service de géocodage
            _logger.info("Géocodage de l'adresse: %s", gps)
            url = "https://nominatim.openstreetmap.org/search"
            params = {'q': gps, 'format': 'json', 'limit': 1}
            res = requests.get(url, params=params, timeout=5, headers={'User-Agent': 'Odoo-DemandeClient-Locator'})
            data = res.json()

            if data:
                lat = float(data[0]['lat'])
                lon = float(data[0]['lon'])
                display_name = data[0].get('display_name', gps)
                _logger.info("Coordonnées géocodées: lat=%s, lon=%s, display_name=%s", lat, lon, display_name)

                return {
                    'latitude': lat,
                    'longitude': lon,
                    'gps_coordinates': display_name
                }
            else:
                _logger.warning("Aucun résultat trouvé pour l'adresse: %s", gps)

        except Exception as e:
            _logger.exception("Erreur lors de la récupération GPS pour %s: %s", gps, str(e))

        return {}

    @api.depends('quantite', 'carriere_materiau_id.prix_tonne', 'camion_id.cout_km', 'distance_km', 'nombre_camions')
    def _compute_cout_total(self):
        _logger.info("Calcul du coût total pour %s demandes", len(self))
        for rec in self:
            cout_materiau = rec.quantite * rec.carriere_materiau_id.prix_tonne if rec.carriere_materiau_id and rec.quantite else 0
            cout_transport = rec.distance_km * rec.camion_id.cout_km * rec.nombre_camions if rec.camion_id and rec.distance_km else 0
            rec.cout_total = cout_materiau + cout_transport
            _logger.info(
                "Coût total calculé pour demande %s: Matériau=%s, Transport=%s, Total=%s",
                rec.id, cout_materiau, cout_transport, rec.cout_total
            )

    @api.depends('quantite', 'camion_id.capacite_tonne')
    def _compute_nombre_camions(self):
        _logger.info("Calcul du nombre de camions pour %s demandes", len(self))
        for rec in self:
            if rec.camion_id and rec.camion_id.capacite_tonne > 0 and rec.quantite > 0:
                rec.nombre_camions = ceil(rec.quantite / rec.camion_id.capacite_tonne)
                _logger.info(
                    "Nombre de camions calculé pour demande %s: Quantité=%s, Capacité=%s, Camions=%s",
                    rec.id, rec.quantite, rec.camion_id.capacite_tonne, rec.nombre_camions
                )
            else:
                rec.nombre_camions = 0
                _logger.info(
                    "Nombre de camions mis à 0 pour demande %s (quantité=%s, capacité=%s)",
                    rec.id, rec.quantite, rec.camion_id.capacite_tonne if rec.camion_id else 0
                )

    def action_detect_position(self):
        _logger.info("Action détecter position appelée pour demande %s", self.id)
        return True

    def action_find_nearest_carriere(self):
        _logger.info("Recherche de la carrière la plus proche pour demande %s", self.id)

        # Vérification des coordonnées du client
        if not self.latitude or not self.longitude:
            _logger.error("Coordonnées manquantes pour la demande %s: latitude=%s, longitude=%s",
                          self.id, self.latitude, self.longitude)
            raise UserError("Position du client manquante.")

        _logger.info("Coordonnées client - Latitude: %s, Longitude: %s", self.latitude, self.longitude)

        ors_key = 'eyJvcmciOiI1YjNjZTM1OTc4NTExMTAwMDFjZjYyNDgiLCJpZCI6Ijc0YzAwNjNmZDA3YzRiNTBhNWQzM2I3NDBlY2FmOGYwIiwiaCI6Im11cm11cjY0In0'
        ors_url = 'https://api.openrouteservice.org/v2/directions/driving-car'

        coords_client = [self.longitude, self.latitude]
        _logger.info("Coordonnées client pour ORS: %s", coords_client)

        min_distance = float('inf')
        nearest_carriere = None

        # Récupération des carrières avec coordonnées valides
        # Si un matériau est sélectionné, filtrer les carrières qui ont ce matériau
        domain = [('latitude', '!=', False), ('longitude', '!=', False)]
        if self.carriere_materiau_id:
            carrieres_ids = self.carriere_materiau_id.mapped('carriere_id').ids
            domain.append(('id', 'in', carrieres_ids))
            _logger.info("Filtrage par matériau ID %s, carrières concernées: %s", self.carriere_materiau_id.id,
                         carrieres_ids)

        carrieres = self.env['carriere'].search(domain)
        _logger.info("%s carrières trouvées avec coordonnées valides", len(carrieres))

        for carriere in carrieres:
            coords_carriere = [carriere.longitude, carriere.latitude]
            _logger.info("Test de la carrière %s (ID: %s) - Coordonnées: %s", carriere.name, carriere.id,
                         coords_carriere)

            try:
                headers = {'Authorization': ors_key, 'Content-Type': 'application/json'}
                body = {"coordinates": [coords_client, coords_carriere]}

                _logger.debug("Requête ORS - URL: %s, Body: %s", ors_url, body)
                response = requests.post(ors_url, json=body, headers=headers, timeout=10)

                if response.status_code != 200:
                    _logger.warning("ORS non-200 pour carrière %s: %s", carriere.name, response.text)
                    continue

                data = response.json()
                segments = data.get('features', [])[0].get('properties', {}).get('segments', [])

                if not segments:
                    _logger.warning("Aucun segment ORS pour carrière %s", carriere.name)
                    continue

                distance_m = segments[0]['distance']
                distance_km = distance_m / 1000.0
                _logger.info("Distance calculée pour carrière %s: %s km", carriere.name, distance_km)

                if distance_km < min_distance:
                    min_distance = distance_km
                    nearest_carriere = carriere
                    _logger.info("Nouvelle carrière la plus proche: %s (%s km)", carriere.name, distance_km)

            except Exception as e:
                _logger.exception("Erreur OpenRouteService pour carrière %s: %s", carriere.name, str(e))
                continue

        if nearest_carriere:
            _logger.info(
                "Mise à jour de la demande %s avec la carrière %s (distance: %s km)",
                self.id, nearest_carriere.name, min_distance
            )

            # Mise à jour des champs, mais en préservant le matériau sélectionné
            update_vals = {
                'carriere_id': nearest_carriere.id,
                'distance_km': round(min_distance, 2)
            }

            # Vérifier si le matériau actuel est disponible dans la nouvelle carrière
            if self.carriere_materiau_id:
                materiau_disponible = self.env['carriere.materiau'].search([
                    ('carriere_id', '=', nearest_carriere.id),
                    ('id', '=', self.carriere_materiau_id.id)
                ])
                if not materiau_disponible:
                    _logger.info(
                        "Le matériau ID %s n'est pas disponible dans la carrière %s, réinitialisation du matériau",
                        self.carriere_materiau_id.id, nearest_carriere.name)
                    # Si le matériau n'est pas disponible, on le réinitialise
                    update_vals['carriere_materiau_id'] = False

            self.write(update_vals)
            _logger.info("Demande %s mise à jour avec succès", self.id)
        else:
            _logger.error("Aucune carrière valide trouvée pour la demande %s", self.id)
            raise UserError("Impossible de trouver une carrière avec une distance valide.")