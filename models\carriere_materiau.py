from odoo import models, fields

class CarriereMateriau(models.Model):
    _name = 'carriere.materiau'
    _description = 'Matériau extrait spécifique à une carrière'

    carriere_id = fields.Many2one('carriere', string="Carrière", required=True)
    materiau_id = fields.Many2one('materiau', string="Type de matériau", required=True)
    prix_tonne = fields.Float(string="Prix par tonne", required=True)
    prix_unitaire = fields.Float(string="Prix unitaire", required=True)

    carriere_materiau_id = fields.Many2one(
        'carriere.materiau',
        string="Matériau extrait",
        domain="[('carriere_id', '=', carriere_id)]",
        required=True
    )

    quantite_disponible = fields.Float(string="Quantité disponible (tonnes)")
