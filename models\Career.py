from odoo import models, fields, api
import requests
import logging

_logger = logging.getLogger(__name__)

class Carriere(models.Model):
    _name = 'carriere'
    _description = 'Carrière partenaire'

    name = fields.Char(string="Nom de la carrière", required=True)
    gps_coordinates = fields.Char(string="Coordonnées GPS")
    material_ids = fields.One2many('carriere.materiau', 'carriere_id', string="Matériaux disponibles")
    latitude = fields.Float(string="Latitude")
    longitude = fields.Float(string="Longitude")

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            updated_vals = self._update_lat_lon_from_gps(vals.get('gps_coordinates'))
            vals.update(updated_vals)
        return super().create(vals_list)

    def write(self, vals):
        if 'gps_coordinates' in vals:
            updated_vals = self._update_lat_lon_from_gps(vals.get('gps_coordinates'))
            vals.update(updated_vals)
        return super().write(vals)

    def _update_lat_lon_from_gps(self, gps):
        if not gps:
            return {'latitude': False, 'longitude': False}
        url = "https://nominatim.openstreetmap.org/search"
        params = {
            'q': gps,
            'format': 'json',
            'limit': 1,
        }
        try:
            res = requests.get(url, params=params, timeout=5, headers={
                'User-Agent': 'Odoo-Carriere-Locator'
            })
            data = res.json()
            if data:
                return {
                    'latitude': float(data[0]['lat']),
                    'longitude': float(data[0]['lon']),
                    'gps_coordinates': data[0].get('display_name', gps)
                }
        except Exception as e:
            _logger.warning("Erreur lors de la récupération GPS : %s", str(e))
        return {}  # en cas d'erreur on ne modifie rien
