<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_form_demande_client" model="ir.ui.view">
        <field name="name">demande.client.form</field>
        <field name="model">demande.client</field>
        <field name="arch" type="xml">
            <form string="Demande de Matériaux">
                <header>
                    <!-- Les boutons sont maintenant gérés par JavaScript -->
                </header>
                <sheet>
                    <group>
                        <field name="name_client"/>
                    </group>
                    <group string="Coordonnées GPS">
                        <field name="gps_coordinates" placeholder="Ex: 36.8011, 10.1851"/>
                        <field name="latitude" readonly="1"/>
                        <field name="longitude" readonly="1"/>
                        <div class="alert alert-warning" attrs="{'invisible': [('latitude', '!=', False)]}">
                            Cliquez sur "📍 Afficher carte + position" pour détecter vos coordonnées
                        </div>
                    </group>
                    <group>
                        <field name="carriere_id"/>
                        <field name="carriere_materiau_id"/>
                        <field name="quantite"/>
                        <field name="distance_km" readonly="1" force_save="1"/>
                        <field name="camion_id"/>
                        <field name="nombre_camions" readonly="1"/>
                        <field name="cout_total" readonly="1"/>
                    </group>
                    <notebook>
                        <page string="Carte">
                            <div name="map_container"
                                 class="o_leaflet_map"
                                 style="height: 400px; border: 1px solid #ccc; margin-top: 10px;">
                                <!-- La carte sera injectée ici via JS -->
                            </div>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
</odoo>