# -*- coding: utf-8 -*-
from odoo import http
from odoo.http import request
import logging
from math import radians, sin, cos, sqrt, atan2
import json

_logger = logging.getLogger(__name__)


class DemandeClientController(http.Controller):
    @http.route('/demande_client/set_position', type='json', auth='user', csrf=False)
    def set_position(self, res_id=None, latitude=None, longitude=None):
        """
        Reçoit latitude et longitude depuis la carte et enregistre dans le modèle demande.client.
        Appelé par le JS lors du clic sur 'Afficher carte + position'.
        """
        try:
            _logger.info(
                "set_position appelé: res_id=%s, lat=%s, lon=%s",
                res_id, latitude, longitude
            )
            if not res_id or latitude is None or longitude is None:
                return {'success': False, 'error': 'Paramètres manquants'}
            res_id = int(res_id)
            record = request.env['demande.client'].browse(res_id)
            if not record.exists():
                return {'success': False, 'error': 'Demande introuvable'}
            # CORRECTION: Utiliser check_access_rights au lieu de check_access pour Odoo 18
            if not record.check_access_rights('write', raise_exception=False):
                _logger.warning(
                    "Utilisateur %s n'a pas les droits d'écriture sur demande %s",
                    request.env.user.id, res_id
                )
                return {'success': False, 'error': 'Droits insuffisants'}
            gps_coordinates = f"{float(latitude)}, {float(longitude)}"
            # Utiliser sudo pour contourner les éventuelles restrictions de champ
            record.sudo().write({
                'latitude': float(latitude),
                'longitude': float(longitude),
                'gps_coordinates': gps_coordinates
            })
            _logger.info(
                "Position mise à jour pour demande %s - Lat: %s, Lng: %s, GPS: %s",
                res_id, latitude, longitude, gps_coordinates
            )
            # Vérifier que les données ont bien été enregistrées
            updated_record = request.env['demande.client'].browse(res_id)
            _logger.info(
                "Vérification après enregistrement - Lat: %s, Lng: %s",
                updated_record.latitude, updated_record.longitude
            )
            return {'success': True}
        except Exception as e:
            _logger.exception("Erreur dans set_position")
            return {'success': False, 'error': str(e)}


class CarriereGeoController(http.Controller):
    @http.route('/carrieres/geo', type='json', auth='user', csrf=False)
    def get_carrieres_with_distance(self, client_lat=None, client_lng=None, carriere_materiau_id=None):
        """
        Renvoie la liste des carrières avec leur distance par rapport au client.
        Filtrer par matériau si spécifié.
        """
        try:
            # Vérifier que les coordonnées sont fournies
            if client_lat is None or client_lng is None:
                return {'error': 'Coordonnées client manquantes'}
            client_lat = float(client_lat)
            client_lng = float(client_lng)
            _logger.info("Recherche de carrières pour client - Lat: %s, Lng: %s, Matériau: %s",
                         client_lat, client_lng, carriere_materiau_id)
        except (TypeError, ValueError) as e:
            _logger.error("Coordonnées client invalides: %s, %s - Erreur: %s", client_lat, client_lng, str(e))
            return {'error': 'Coordonnées client invalides'}
        try:
            # Domaine de base pour les carrières avec coordonnées valides
            domain = [
                ('latitude', '!=', False),
                ('longitude', '!=', False)
            ]

            # Si un matériau est spécifié, filtrer les carrières qui ont ce matériau
            if carriere_materiau_id:
                # CORRECTION: Gérer le cas où carriere_materiau_id est une liste
                if isinstance(carriere_materiau_id, list):
                    # Si c'est une liste, prendre le premier élément
                    carriere_materiau_id = carriere_materiau_id[0] if carriere_materiau_id else None

                # S'assurer que carriere_materiau_id est un entier
                if carriere_materiau_id:
                    try:
                        carriere_materiau_id = int(carriere_materiau_id)
                        # Récupérer le matériau de base à partir de carriere_materiau_id
                        carriere_materiau = request.env['carriere.materiau'].browse(carriere_materiau_id)
                        if carriere_materiau.exists():
                            materiau_id = carriere_materiau.materiau_id.id
                            _logger.info("Matériau de base ID: %s", materiau_id)

                            # Récupérer toutes les carrières qui ont ce matériau
                            carriere_materiaux = request.env['carriere.materiau'].search([
                                ('materiau_id', '=', materiau_id)
                            ])
                            carrieres_ids = carriere_materiaux.mapped('carriere_id').ids
                            if carrieres_ids:
                                domain.append(('id', 'in', carrieres_ids))
                                _logger.info("Filtrage par matériau ID %s, carrières concernées: %s", materiau_id,
                                             carrieres_ids)
                    except (ValueError, TypeError) as e:
                        _logger.error("Erreur de conversion du carriere_materiau_id: %s - Erreur: %s",
                                      carriere_materiau_id, str(e))
                        # En cas d'erreur, continuer sans filtrer par matériau
                        pass

            # Récupérer les carrières
            carrieres = request.env['carriere'].search(domain)
            _logger.info("Nombre de carrières trouvées: %s", len(carrieres))

            result = []
            for carriere in carrieres:
                try:
                    distance = self._haversine_distance(
                        client_lat, client_lng,
                        carriere.latitude, carriere.longitude
                    )
                    result.append({
                        'id': carriere.id,
                        'name': carriere.name,
                        'latitude': carriere.latitude,
                        'longitude': carriere.longitude,
                        'distance_km': round(distance, 2)
                    })
                except Exception as e:
                    _logger.error("Erreur lors du calcul de distance pour la carrière %s: %s", carriere.name, str(e))
                    continue

            # Trier par distance croissante
            result.sort(key=lambda x: x['distance_km'])
            _logger.info("Résultats retournés: %s", result)
            return result
        except Exception as e:
            _logger.exception("Erreur dans get_carrieres_with_distance: %s", str(e))
            return {'error': f'Erreur serveur: {str(e)}'}

    def _haversine_distance(self, lat1, lon1, lat2, lon2):
        """
        Calcule la distance en km entre deux points (lat/lon) avec la formule de Haversine.
        """
        R = 6371  # Rayon moyen de la Terre en km
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
        c = 2 * atan2(sqrt(a), sqrt(1 - a))
        return R * c